import * as SQLite from 'expo-sqlite';
import {
    Activity,
    DatabaseActivity,
    DatabaseLongTermGoal,
    DatabaseTag,
    LongTermGoal,
    Tag
} from '../types';

class Database {
  private db: SQLite.SQLiteDatabase | null = null;

  async init(): Promise<void> {
    try {
      this.db = await SQLite.openDatabaseAsync('candyYourself.db');
      await this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Activities table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS activities (
        id TEXT PRIMARY KEY,
        tag_name TEXT NOT NULL,
        tag_type TEXT NOT NULL,
        note TEXT,
        point INTEGER NOT NULL,
        tag_id TEXT,
        goal_id TEXT,
        type TEXT NOT NULL,
        created_at TEXT NOT NULL
      );
    `);

    // Tags table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS tags (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL,
        order_index INTEGER NOT NULL,
        created_at TEXT NOT NULL
      );
    `);

    // Long term goals table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS long_term_goals (
        id TEXT PRIMARY KEY,
        tag_id TEXT NOT NULL,
        target_no INTEGER NOT NULL,
        current_no INTEGER NOT NULL DEFAULT 0,
        point INTEGER NOT NULL,
        start_time TEXT NOT NULL,
        end_time TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'inprogress',
        created_at TEXT NOT NULL
      );
    `);

    // App settings table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS app_settings (
        id TEXT PRIMARY KEY,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL
      );
    `);

    // Visits table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS visits (
        id TEXT PRIMARY KEY,
        visit_time TEXT NOT NULL,
        count INTEGER NOT NULL
      );
    `);

    // Remaining points table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS remaining_points (
        id TEXT PRIMARY KEY,
        point INTEGER NOT NULL,
        updated_at TEXT NOT NULL
      );
    `);

    // Wishes table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS wishes (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        point INTEGER NOT NULL,
        note TEXT,
        created_at TEXT NOT NULL
      );
    `);

    // Create indexes for better performance
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);
      CREATE INDEX IF NOT EXISTS idx_activities_tag_type ON activities(tag_type);
      CREATE INDEX IF NOT EXISTS idx_tags_type ON tags(type);
      CREATE INDEX IF NOT EXISTS idx_goals_status ON long_term_goals(status);
    `);
  }

  // Helper methods to convert between database and app types
  private dbActivityToActivity(dbActivity: DatabaseActivity): Activity {
    return {
      id: dbActivity.id,
      tagName: dbActivity.tag_name,
      tagType: dbActivity.tag_type as 'pain' | 'gain',
      note: dbActivity.note || undefined,
      point: dbActivity.point,
      tagId: dbActivity.tag_id || undefined,
      goalId: dbActivity.goal_id || undefined,
      type: dbActivity.type as 'task' | 'goal',
      createdAt: new Date(dbActivity.created_at)
    };
  }

  private activityToDbActivity(activity: Activity): Omit<DatabaseActivity, 'id'> {
    return {
      tag_name: activity.tagName,
      tag_type: activity.tagType,
      note: activity.note || null,
      point: activity.point,
      tag_id: activity.tagId || null,
      goal_id: activity.goalId || null,
      type: activity.type,
      created_at: activity.createdAt.toISOString()
    };
  }

  private dbTagToTag(dbTag: DatabaseTag): Tag {
    return {
      id: dbTag.id,
      name: dbTag.name,
      type: dbTag.type as 'pain' | 'gain',
      orderIndex: dbTag.order_index,
      createdAt: new Date(dbTag.created_at)
    };
  }

  private tagToDbTag(tag: Tag): Omit<DatabaseTag, 'id'> {
    return {
      name: tag.name,
      type: tag.type,
      order_index: tag.orderIndex,
      created_at: tag.createdAt.toISOString()
    };
  }

  private dbGoalToGoal(dbGoal: DatabaseLongTermGoal): LongTermGoal {
    return {
      id: dbGoal.id,
      tagId: dbGoal.tag_id,
      targetNo: dbGoal.target_no,
      currentNo: dbGoal.current_no,
      point: dbGoal.point,
      startTime: new Date(dbGoal.start_time),
      endTime: new Date(dbGoal.end_time),
      status: dbGoal.status as 'inprogress' | 'done' | 'cancelled',
      createdAt: new Date(dbGoal.created_at)
    };
  }

  private goalToDbGoal(goal: LongTermGoal): Omit<DatabaseLongTermGoal, 'id'> {
    return {
      tag_id: goal.tagId,
      target_no: goal.targetNo,
      current_no: goal.currentNo,
      point: goal.point,
      start_time: goal.startTime.toISOString(),
      end_time: goal.endTime.toISOString(),
      status: goal.status,
      created_at: goal.createdAt.toISOString()
    };
  }

  // Activity methods
  async insertActivity(activity: Activity): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const dbActivity = this.activityToDbActivity(activity);
    await this.db.runAsync(
      `INSERT INTO activities (id, tag_name, tag_type, note, point, tag_id, goal_id, type, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [activity.id, dbActivity.tag_name, dbActivity.tag_type, dbActivity.note, 
       dbActivity.point, dbActivity.tag_id, dbActivity.goal_id, dbActivity.type, dbActivity.created_at]
    );
  }

  async getActivities(limit: number = 50, offset: number = 0): Promise<Activity[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM activities ORDER BY created_at DESC LIMIT ? OFFSET ?',
      [limit, offset]
    ) as DatabaseActivity[];
    
    return result.map(this.dbActivityToActivity);
  }

  async getActivitiesByDate(date: Date): Promise<Activity[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM activities WHERE created_at >= ? AND created_at <= ? ORDER BY created_at DESC',
      [startOfDay.toISOString(), endOfDay.toISOString()]
    ) as DatabaseActivity[];
    
    return result.map(this.dbActivityToActivity);
  }

  async deleteActivity(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.runAsync('DELETE FROM activities WHERE id = ?', [id]);
  }

  // Tag methods
  async insertTag(tag: Tag): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const dbTag = this.tagToDbTag(tag);
    await this.db.runAsync(
      'INSERT INTO tags (id, name, type, order_index, created_at) VALUES (?, ?, ?, ?, ?)',
      [tag.id, dbTag.name, dbTag.type, dbTag.order_index, dbTag.created_at]
    );
  }

  async getTags(): Promise<Tag[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM tags ORDER BY order_index ASC'
    ) as DatabaseTag[];
    
    return result.map(this.dbTagToTag);
  }

  async getTagsByType(type: 'pain' | 'gain'): Promise<Tag[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM tags WHERE type = ? ORDER BY order_index ASC',
      [type]
    ) as DatabaseTag[];
    
    return result.map(this.dbTagToTag);
  }

  async deleteTag(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.runAsync('DELETE FROM tags WHERE id = ?', [id]);
  }

  // Goal methods
  async insertGoal(goal: LongTermGoal): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const dbGoal = this.goalToDbGoal(goal);
    await this.db.runAsync(
      `INSERT INTO long_term_goals (id, tag_id, target_no, current_no, point, start_time, end_time, status, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [goal.id, dbGoal.tag_id, dbGoal.target_no, dbGoal.current_no, dbGoal.point, 
       dbGoal.start_time, dbGoal.end_time, dbGoal.status, dbGoal.created_at]
    );
  }

  async getGoals(): Promise<LongTermGoal[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getAllAsync(
      'SELECT * FROM long_term_goals WHERE status = ? ORDER BY start_time ASC',
      ['inprogress']
    ) as DatabaseLongTermGoal[];
    
    return result.map(this.dbGoalToGoal);
  }

  async updateGoal(goal: LongTermGoal): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      'UPDATE long_term_goals SET current_no = ?, status = ? WHERE id = ?',
      [goal.currentNo, goal.status, goal.id]
    );
  }

  async deleteGoal(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.runAsync('DELETE FROM long_term_goals WHERE id = ?', [id]);
  }

  // App settings methods
  async getSetting(key: string): Promise<string | null> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getFirstAsync(
      'SELECT value FROM app_settings WHERE key = ?',
      [key]
    ) as { value: string } | null;
    
    return result?.value || null;
  }

  async setSetting(key: string, value: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      'INSERT OR REPLACE INTO app_settings (id, key, value) VALUES (?, ?, ?)',
      [Date.now().toString(), key, value]
    );
  }

  // Visit tracking methods
  async getVisitCount(): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getFirstAsync(
      'SELECT count FROM visits ORDER BY visit_time DESC LIMIT 1'
    ) as { count: number } | null;
    
    return result?.count || 0;
  }

  async updateVisitCount(): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');
    
    const currentCount = await this.getVisitCount();
    const newCount = currentCount + 1;
    
    await this.db.runAsync(
      'INSERT INTO visits (id, visit_time, count) VALUES (?, ?, ?)',
      [Date.now().toString(), new Date().toISOString(), newCount]
    );
    
    return newCount;
  }

  // Balance methods
  async getBalance(): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getFirstAsync(
      'SELECT point FROM remaining_points ORDER BY updated_at DESC LIMIT 1'
    ) as { point: number } | null;
    
    return result?.point || 0;
  }

  async updateBalance(newBalance: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.runAsync(
      'INSERT INTO remaining_points (id, point, updated_at) VALUES (?, ?, ?)',
      [Date.now().toString(), newBalance, new Date().toISOString()]
    );
  }
}

export const database = new Database();
