import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { dataSource } from '../database/DataSource';
import { getSemanticColors, spacing, textStyles } from '../styles';
import {
  GradientBackground,
  Card,
  CardHeader,
  CardContent,
  Button,
  AnimatedScreen,
  useTheme
} from '../components';

interface SettingsItem {
  id: string;
  title: string;
  subtitle?: string;
  type: 'navigation' | 'switch' | 'info';
  value?: boolean;
  onPress?: () => void;
  onValueChange?: (value: boolean) => void;
  icon?: keyof typeof Ionicons.glyphMap;
}

const SettingsScreen: React.FC = () => {
  const { theme, toggleTheme, isSystemTheme, setSystemTheme } = useTheme();
  const colors = getSemanticColors(theme);

  const [isVIP, setIsVIP] = useState<boolean>(false);
  const [visitCount, setVisitCount] = useState<number>(0);
  const [balance, setBalance] = useState<number>(0);
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(false);

  useEffect(() => {
    const updateData = () => {
      setIsVIP(dataSource.getIsVIP());
      setVisitCount(dataSource.getVisitCount());
      setBalance(dataSource.getBalance());
    };

    updateData();
    dataSource.addListener(updateData);

    return () => {
      dataSource.removeListener(updateData);
    };
  }, []);

  const handleVIPToggle = async (value: boolean) => {
    await dataSource.setVIPStatus(value);
    setIsVIP(value);
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your activities, goals, and settings. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear data logic would go here
              Alert.alert('Success', 'All data has been cleared.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data.');
            }
          },
        },
      ]
    );
  };

  const handleExportData = () => {
    Alert.alert(
      'Export Data',
      'This feature will export your data to a file.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            // Export logic would go here
            Alert.alert('Info', 'Export feature coming soon!');
          },
        },
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'About Candy Yourself',
      'Version 2.0.0\n\nA self-discipline app to help you stay motivated by earning points for tasks and redeeming rewards.\n\nBuilt with React Native and Expo.',
      [{ text: 'OK' }]
    );
  };

  const settingsData: SettingsItem[] = [
    {
      id: 'account',
      title: 'Account',
      subtitle: '',
      type: 'info',
      icon: 'person-outline',
    },
    {
      id: 'vip',
      title: 'VIP Status',
      subtitle: isVIP ? 'Premium features enabled' : 'Upgrade to remove ads',
      type: 'switch',
      value: isVIP,
      onValueChange: handleVIPToggle,
      icon: 'star-outline',
    },
    {
      id: 'notifications',
      title: 'Notifications',
      subtitle: 'Receive reminders and updates',
      type: 'switch',
      value: notificationsEnabled,
      onValueChange: setNotificationsEnabled,
      icon: 'notifications-outline',
    },
    {
      id: 'data',
      title: 'Data Management',
      subtitle: '',
      type: 'info',
      icon: 'folder-outline',
    },
    {
      id: 'export',
      title: 'Export Data',
      subtitle: 'Save your data to a file',
      type: 'navigation',
      onPress: handleExportData,
      icon: 'download-outline',
    },
    {
      id: 'clear',
      title: 'Clear All Data',
      subtitle: 'Permanently delete all data',
      type: 'navigation',
      onPress: handleClearData,
      icon: 'trash-outline',
    },
    {
      id: 'support',
      title: 'Support',
      subtitle: '',
      type: 'info',
      icon: 'help-circle-outline',
    },
    {
      id: 'about',
      title: 'About',
      subtitle: 'App version and information',
      type: 'navigation',
      onPress: handleAbout,
      icon: 'information-circle-outline',
    },
  ];

  const renderStatsCard = () => (
    <View style={styles.statsCard}>
      <Text style={styles.statsTitle}>Your Progress</Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{balance}</Text>
          <Text style={styles.statLabel}>Current Balance</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{visitCount}</Text>
          <Text style={styles.statLabel}>App Opens</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{dataSource.getActivities().length}</Text>
          <Text style={styles.statLabel}>Total Activities</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{dataSource.getGoals().length}</Text>
          <Text style={styles.statLabel}>Active Goals</Text>
        </View>
      </View>
    </View>
  );

  const renderSettingsItem = (item: SettingsItem) => {
    if (item.type === 'info') {
      return (
        <View key={item.id} style={styles.sectionHeader}>
          <Ionicons name={item.icon!} size={20} color={colors.primary} />
          <Text style={styles.sectionTitle}>{item.title}</Text>
        </View>
      );
    }

    return (
      <TouchableOpacity
        key={item.id}
        style={styles.settingsItem}
        onPress={item.onPress}
        disabled={item.type === 'switch'}
      >
        <View style={styles.settingsItemLeft}>
          {item.icon && (
            <Ionicons name={item.icon} size={24} color={colors.textSecondary} />
          )}
          <View style={styles.settingsItemText}>
            <Text style={styles.settingsItemTitle}>{item.title}</Text>
            {item.subtitle && (
              <Text style={styles.settingsItemSubtitle}>{item.subtitle}</Text>
            )}
          </View>
        </View>
        
        <View style={styles.settingsItemRight}>
          {item.type === 'switch' ? (
            <Switch
              value={item.value}
              onValueChange={item.onValueChange}
              trackColor={{ false: colors.border, true: colors.primary }}
              thumbColor={colors.background}
            />
          ) : (
            <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Settings</Text>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderStatsCard()}
        
        <View style={styles.settingsList}>
          {settingsData.map(renderSettingsItem)}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingTop: spacing.component.safeAreaTop,
    paddingHorizontal: spacing.layout.screenPadding,
    paddingBottom: spacing.md,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  headerTitle: {
    ...textStyles.h3,
    color: colors.text,
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: spacing.component.tabBarHeight,
  },
  statsCard: {
    backgroundColor: colors.backgroundSecondary,
    margin: spacing.layout.screenPadding,
    borderRadius: spacing.component.radiusLG,
    padding: spacing.lg,
    ...commonStyles.shadowMedium,
  },
  statsTitle: {
    ...textStyles.h4,
    color: colors.text,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  statValue: {
    ...textStyles.h3,
    color: colors.primary,
    fontWeight: 'bold',
  },
  statLabel: {
    ...textStyles.caption,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  settingsList: {
    paddingHorizontal: spacing.layout.screenPadding,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.sm,
  },
  sectionTitle: {
    ...textStyles.h5,
    color: colors.primary,
    marginLeft: spacing.sm,
  },
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.background,
    borderRadius: spacing.component.radiusMD,
    marginBottom: spacing.xs,
    ...commonStyles.shadowSmall,
  },
  settingsItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingsItemText: {
    marginLeft: spacing.md,
    flex: 1,
  },
  settingsItemTitle: {
    ...textStyles.body,
    color: colors.text,
  },
  settingsItemSubtitle: {
    ...textStyles.bodySmall,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  settingsItemRight: {
    marginLeft: spacing.md,
  },
});

export default SettingsScreen;
