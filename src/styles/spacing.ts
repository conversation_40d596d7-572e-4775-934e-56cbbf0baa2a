// Modern spacing system with enhanced design tokens
export const spacing = {
  // Base spacing unit (4px) - Following 8pt grid system
  unit: 4,

  // Enhanced spacing scale with more granular options
  '0': 0,    // 0px
  '0.5': 2,  // 2px
  '1': 4,    // 4px
  '1.5': 6,  // 6px
  '2': 8,    // 8px
  '2.5': 10, // 10px
  '3': 12,   // 12px
  '3.5': 14, // 14px
  '4': 16,   // 16px
  '5': 20,   // 20px
  '6': 24,   // 24px
  '7': 28,   // 28px
  '8': 32,   // 32px
  '9': 36,   // 36px
  '10': 40,  // 40px
  '11': 44,  // 44px
  '12': 48,  // 48px
  '14': 56,  // 56px
  '16': 64,  // 64px
  '20': 80,  // 80px
  '24': 96,  // 96px
  '28': 112, // 112px
  '32': 128, // 128px
  '36': 144, // 144px
  '40': 160, // 160px
  '44': 176, // 176px
  '48': 192, // 192px
  '52': 208, // 208px
  '56': 224, // 224px
  '60': 240, // 240px
  '64': 256, // 256px
  '72': 288, // 288px
  '80': 320, // 320px
  '96': 384, // 384px

  // Legacy spacing for backward compatibility
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 80,
  '5xl': 96,

  // Component-specific spacing with modern tokens
  component: {
    // Border radius - Enhanced with more options
    radiusNone: 0,
    radiusXS: 2,
    radiusSM: 4,
    radiusMD: 8,
    radiusLG: 12,
    radiusXL: 16,
    radius2XL: 20,
    radius3XL: 24,
    radiusFull: 9999,

    // Button spacing - Improved hierarchy
    buttonPaddingSmallVertical: 6,
    buttonPaddingSmallHorizontal: 12,
    buttonPaddingVertical: 10,
    buttonPaddingHorizontal: 16,
    buttonPaddingLargeVertical: 14,
    buttonPaddingLargeHorizontal: 24,
    buttonPaddingXLVertical: 18,
    buttonPaddingXLHorizontal: 32,

    // Input spacing - Better touch targets
    inputPaddingSmallVertical: 8,
    inputPaddingSmallHorizontal: 12,
    inputPaddingVertical: 12,
    inputPaddingHorizontal: 16,
    inputPaddingLargeVertical: 16,
    inputPaddingLargeHorizontal: 20,

    // Card spacing - Modern card design
    cardPaddingSmall: 12,
    cardPadding: 16,
    cardPaddingLarge: 24,
    cardMarginSmall: 8,
    cardMargin: 16,
    cardMarginLarge: 24,

    // List spacing - Better visual hierarchy
    listItemPaddingSmall: 12,
    listItemPadding: 16,
    listItemPaddingLarge: 20,
    listItemMargin: 4,
    listSectionSpacing: 24,

    // Section spacing - Improved content organization
    sectionPaddingSmall: 16,
    sectionPadding: 24,
    sectionPaddingLarge: 32,
    sectionMarginSmall: 20,
    sectionMargin: 32,
    sectionMarginLarge: 48,

    // Navigation spacing - Modern navigation design
    tabBarHeight: 88,
    tabBarPaddingBottom: 34, // Safe area for iPhone
    tabBarPaddingTop: 10,
    tabBarIconSize: 24,
    tabBarLabelSpacing: 4,

    // Header spacing - Enhanced header design
    headerHeight: 44,
    headerHeightLarge: 56,
    headerPadding: 16,
    headerPaddingLarge: 20,
    headerTitleSpacing: 8,

    // Safe areas - Device-specific spacing
    safeAreaTop: 44,
    safeAreaBottom: 34,
    safeAreaSides: 16,

    // Modal and overlay spacing
    modalPadding: 20,
    modalMargin: 16,
    overlayPadding: 24,

    // Form spacing
    formFieldSpacing: 16,
    formSectionSpacing: 32,
    formLabelSpacing: 8,

    // Icon spacing
    iconSpacingSmall: 4,
    iconSpacing: 8,
    iconSpacingLarge: 12,

    // Divider spacing
    dividerSpacing: 16,
    dividerThickness: 1,
  },

  // Layout spacing - Enhanced layout system
  layout: {
    // Screen-level spacing
    screenPaddingSmall: 16,
    screenPadding: 20,
    screenPaddingLarge: 24,

    // Container spacing
    containerPaddingSmall: 12,
    containerPadding: 16,
    containerPaddingLarge: 20,

    // Content spacing
    contentSpacingSmall: 16,
    contentSpacing: 24,
    contentSpacingLarge: 32,

    // Grid spacing
    gridGap: 16,
    gridGapSmall: 8,
    gridGapLarge: 24,

    // Section spacing
    sectionSpacing: 24,
    sectionSpacingLarge: 40,

    // Item spacing
    itemSpacingSmall: 8,
    itemSpacing: 12,
    itemSpacingLarge: 16,
  },

  // Animation and interaction spacing
  interaction: {
    // Touch target minimum size
    touchTargetMinSize: 44,

    // Hover and focus spacing
    focusOutlineWidth: 2,
    focusOutlineOffset: 2,

    // Ripple effect spacing
    rippleRadius: 20,
  },
} as const;

// Enhanced helper functions for spacing calculations
export const getSpacing = (multiplier: number): number => {
  return spacing.unit * multiplier;
};

// Get spacing by key with type safety
export const getSpacingValue = (key: keyof typeof spacing): number => {
  const value = spacing[key];
  return typeof value === 'number' ? value : 0;
};

// Legacy component spacing helper
export const getComponentSpacing = (size: 'xs' | 'sm' | 'md' | 'lg' | 'xl'): number => {
  switch (size) {
    case 'xs':
      return spacing.xs;
    case 'sm':
      return spacing.sm;
    case 'md':
      return spacing.md;
    case 'lg':
      return spacing.lg;
    case 'xl':
      return spacing.xl;
    default:
      return spacing.md;
  }
};

// Modern spacing helpers
export const getPadding = (size: 'small' | 'medium' | 'large' = 'medium') => {
  switch (size) {
    case 'small':
      return {
        paddingVertical: spacing['2'],
        paddingHorizontal: spacing['3'],
      };
    case 'medium':
      return {
        paddingVertical: spacing['3'],
        paddingHorizontal: spacing['4'],
      };
    case 'large':
      return {
        paddingVertical: spacing['4'],
        paddingHorizontal: spacing['6'],
      };
  }
};

export const getMargin = (size: 'small' | 'medium' | 'large' = 'medium') => {
  switch (size) {
    case 'small':
      return spacing['2'];
    case 'medium':
      return spacing['4'];
    case 'large':
      return spacing['6'];
  }
};

export const getBorderRadius = (size: 'small' | 'medium' | 'large' | 'full' = 'medium') => {
  switch (size) {
    case 'small':
      return spacing.component.radiusSM;
    case 'medium':
      return spacing.component.radiusMD;
    case 'large':
      return spacing.component.radiusLG;
    case 'full':
      return spacing.component.radiusFull;
  }
};

// Responsive spacing helpers
export const getResponsiveSpacing = (base: number, scale: number = 1): number => {
  return Math.round(base * scale);
};

// Grid spacing helpers
export const getGridSpacing = (columns: number, containerWidth: number, gap: number = spacing.layout.gridGap): number => {
  return (containerWidth - (gap * (columns - 1))) / columns;
};

// Safe area helpers
export const getSafeAreaSpacing = (position: 'top' | 'bottom' | 'sides') => {
  switch (position) {
    case 'top':
      return spacing.component.safeAreaTop;
    case 'bottom':
      return spacing.component.safeAreaBottom;
    case 'sides':
      return spacing.component.safeAreaSides;
  }
};
