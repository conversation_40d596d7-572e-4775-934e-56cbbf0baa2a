import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { getSemanticColors } from '../styles/colors';
import { spacing } from '../styles/spacing';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'small' | 'medium' | 'large';
  margin?: 'none' | 'small' | 'medium' | 'large';
  borderRadius?: 'small' | 'medium' | 'large' | 'full';
  onPress?: () => void;
  disabled?: boolean;
  style?: ViewStyle;
  theme?: 'light' | 'dark';
}

export const Card: React.FC<CardProps> = ({
  children,
  variant = 'elevated',
  padding = 'medium',
  margin = 'medium',
  borderRadius = 'medium',
  onPress,
  disabled = false,
  style,
  theme = 'light',
}) => {
  const colors = getSemanticColors(theme);

  const getCardStyles = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: colors.surface,
    };

    // Variant styles
    switch (variant) {
      case 'elevated':
        baseStyle.shadowColor = colors.shadow;
        baseStyle.shadowOffset = { width: 0, height: 2 };
        baseStyle.shadowOpacity = 0.1;
        baseStyle.shadowRadius = 8;
        baseStyle.elevation = 3;
        break;
      case 'outlined':
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = colors.border;
        break;
      case 'filled':
        baseStyle.backgroundColor = colors.surfaceVariant;
        break;
    }

    // Padding
    switch (padding) {
      case 'none':
        break;
      case 'small':
        baseStyle.padding = spacing.component.cardPaddingSmall;
        break;
      case 'large':
        baseStyle.padding = spacing.component.cardPaddingLarge;
        break;
      default: // medium
        baseStyle.padding = spacing.component.cardPadding;
    }

    // Margin
    switch (margin) {
      case 'none':
        break;
      case 'small':
        baseStyle.margin = spacing.component.cardMarginSmall;
        break;
      case 'large':
        baseStyle.margin = spacing.component.cardMarginLarge;
        break;
      default: // medium
        baseStyle.margin = spacing.component.cardMargin;
    }

    // Border radius
    switch (borderRadius) {
      case 'small':
        baseStyle.borderRadius = spacing.component.radiusSM;
        break;
      case 'large':
        baseStyle.borderRadius = spacing.component.radiusLG;
        break;
      case 'full':
        baseStyle.borderRadius = spacing.component.radiusFull;
        break;
      default: // medium
        baseStyle.borderRadius = spacing.component.radiusMD;
    }

    // Disabled state
    if (disabled) {
      baseStyle.opacity = 0.5;
    }

    return baseStyle;
  };

  const cardStyle = [getCardStyles(), style];

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyle}
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return <View style={cardStyle}>{children}</View>;
};

// Card sub-components for better composition
export const CardHeader: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
}> = ({ children, style }) => (
  <View style={[styles.header, style]}>{children}</View>
);

export const CardContent: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
}> = ({ children, style }) => (
  <View style={[styles.content, style]}>{children}</View>
);

export const CardFooter: React.FC<{
  children: React.ReactNode;
  style?: ViewStyle;
}> = ({ children, style }) => (
  <View style={[styles.footer, style]}>{children}</View>
);

const styles = StyleSheet.create({
  header: {
    marginBottom: spacing['3'],
  },
  content: {
    flex: 1,
  },
  footer: {
    marginTop: spacing['3'],
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
});

export default Card;
