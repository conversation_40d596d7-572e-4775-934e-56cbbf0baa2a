import React, { useEffect, useRef } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  ViewStyle,
  Dimensions,
} from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface AnimatedScreenProps {
  children: React.ReactNode;
  animationType?: 'fade' | 'slide' | 'scale' | 'bounce';
  duration?: number;
  delay?: number;
  style?: ViewStyle;
}

export const AnimatedScreen: React.FC<AnimatedScreenProps> = ({
  children,
  animationType = 'fade',
  duration = 300,
  delay = 0,
  style,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    const animations: Animated.CompositeAnimation[] = [];

    switch (animationType) {
      case 'fade':
        animations.push(
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration,
            useNativeDriver: true,
          })
        );
        break;
      case 'slide':
        animations.push(
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
              toValue: 0,
              duration,
              useNativeDriver: true,
            }),
          ])
        );
        break;
      case 'scale':
        animations.push(
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1,
              tension: 100,
              friction: 8,
              useNativeDriver: true,
            }),
          ])
        );
        break;
      case 'bounce':
        animations.push(
          Animated.parallel([
            Animated.timing(fadeAnim, {
              toValue: 1,
              duration: duration * 0.6,
              useNativeDriver: true,
            }),
            Animated.spring(scaleAnim, {
              toValue: 1,
              tension: 150,
              friction: 6,
              useNativeDriver: true,
            }),
          ])
        );
        break;
    }

    const startAnimation = () => {
      Animated.sequence([
        Animated.delay(delay),
        ...animations,
      ]).start();
    };

    startAnimation();
  }, [animationType, duration, delay, fadeAnim, slideAnim, scaleAnim]);

  const getAnimatedStyle = () => {
    const baseStyle = {
      opacity: fadeAnim,
    };

    switch (animationType) {
      case 'slide':
        return {
          ...baseStyle,
          transform: [{ translateY: slideAnim }],
        };
      case 'scale':
      case 'bounce':
        return {
          ...baseStyle,
          transform: [{ scale: scaleAnim }],
        };
      default:
        return baseStyle;
    }
  };

  return (
    <Animated.View style={[styles.container, getAnimatedStyle(), style]}>
      {children}
    </Animated.View>
  );
};

// Staggered animation for lists
interface StaggeredAnimationProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  animationType?: 'fade' | 'slide' | 'scale';
  style?: ViewStyle;
}

export const StaggeredAnimation: React.FC<StaggeredAnimationProps> = ({
  children,
  staggerDelay = 100,
  animationType = 'slide',
  style,
}) => {
  return (
    <View style={style}>
      {children.map((child, index) => (
        <AnimatedScreen
          key={index}
          animationType={animationType}
          delay={index * staggerDelay}
          duration={300}
        >
          {child}
        </AnimatedScreen>
      ))}
    </View>
  );
};

// Page transition wrapper
interface PageTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  transitionType?: 'slideLeft' | 'slideRight' | 'slideUp' | 'slideDown' | 'fade';
  duration?: number;
  style?: ViewStyle;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  isVisible,
  transitionType = 'slideRight',
  duration = 300,
  style,
}) => {
  const translateX = useRef(new Animated.Value(screenWidth)).current;
  const translateY = useRef(new Animated.Value(screenHeight)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isVisible) {
      const animations = [];

      switch (transitionType) {
        case 'slideLeft':
          translateX.setValue(-screenWidth);
          animations.push(
            Animated.timing(translateX, {
              toValue: 0,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'slideRight':
          translateX.setValue(screenWidth);
          animations.push(
            Animated.timing(translateX, {
              toValue: 0,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'slideUp':
          translateY.setValue(screenHeight);
          animations.push(
            Animated.timing(translateY, {
              toValue: 0,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'slideDown':
          translateY.setValue(-screenHeight);
          animations.push(
            Animated.timing(translateY, {
              toValue: 0,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'fade':
          animations.push(
            Animated.timing(opacity, {
              toValue: 1,
              duration,
              useNativeDriver: true,
            })
          );
          break;
      }

      Animated.parallel(animations).start();
    } else {
      // Hide animation
      const hideAnimations = [];

      switch (transitionType) {
        case 'slideLeft':
          hideAnimations.push(
            Animated.timing(translateX, {
              toValue: screenWidth,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'slideRight':
          hideAnimations.push(
            Animated.timing(translateX, {
              toValue: -screenWidth,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'slideUp':
          hideAnimations.push(
            Animated.timing(translateY, {
              toValue: -screenHeight,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'slideDown':
          hideAnimations.push(
            Animated.timing(translateY, {
              toValue: screenHeight,
              duration,
              useNativeDriver: true,
            })
          );
          break;
        case 'fade':
          hideAnimations.push(
            Animated.timing(opacity, {
              toValue: 0,
              duration,
              useNativeDriver: true,
            })
          );
          break;
      }

      Animated.parallel(hideAnimations).start();
    }
  }, [isVisible, transitionType, duration, translateX, translateY, opacity]);

  const getTransformStyle = () => {
    switch (transitionType) {
      case 'slideLeft':
      case 'slideRight':
        return { transform: [{ translateX }] };
      case 'slideUp':
      case 'slideDown':
        return { transform: [{ translateY }] };
      case 'fade':
        return { opacity };
      default:
        return {};
    }
  };

  return (
    <Animated.View style={[styles.pageContainer, getTransformStyle(), style]}>
      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default AnimatedScreen;
