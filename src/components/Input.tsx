import React, { useState } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';
import { getSemanticColors } from '../styles/colors';
import { textStyles } from '../styles/typography';
import { spacing } from '../styles/spacing';

export interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'outlined' | 'filled' | 'underlined';
  size?: 'small' | 'medium' | 'large';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  theme?: 'light' | 'dark';
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  variant = 'outlined',
  size = 'medium',
  leftIcon,
  rightIcon,
  fullWidth = true,
  containerStyle,
  inputStyle,
  labelStyle,
  theme = 'light',
  onFocus,
  onBlur,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const colors = getSemanticColors(theme);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const getContainerStyles = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
    };

    // Size styles
    switch (size) {
      case 'small':
        baseStyle.paddingVertical = spacing.component.inputPaddingSmallVertical;
        baseStyle.paddingHorizontal = spacing.component.inputPaddingSmallHorizontal;
        baseStyle.minHeight = 36;
        break;
      case 'large':
        baseStyle.paddingVertical = spacing.component.inputPaddingLargeVertical;
        baseStyle.paddingHorizontal = spacing.component.inputPaddingLargeHorizontal;
        baseStyle.minHeight = 52;
        break;
      default: // medium
        baseStyle.paddingVertical = spacing.component.inputPaddingVertical;
        baseStyle.paddingHorizontal = spacing.component.inputPaddingHorizontal;
        baseStyle.minHeight = 44;
    }

    // Variant styles
    switch (variant) {
      case 'outlined':
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = error ? colors.error : isFocused ? colors.primary : colors.border;
        baseStyle.borderRadius = spacing.component.radiusMD;
        baseStyle.backgroundColor = colors.background;
        break;
      case 'filled':
        baseStyle.backgroundColor = colors.surfaceVariant;
        baseStyle.borderRadius = spacing.component.radiusMD;
        baseStyle.borderBottomWidth = 2;
        baseStyle.borderBottomColor = error ? colors.error : isFocused ? colors.primary : 'transparent';
        break;
      case 'underlined':
        baseStyle.borderBottomWidth = 1;
        baseStyle.borderBottomColor = error ? colors.error : isFocused ? colors.primary : colors.border;
        baseStyle.backgroundColor = 'transparent';
        break;
    }

    // Full width
    if (fullWidth) {
      baseStyle.width = '100%';
    }

    // Disabled state
    if (textInputProps.editable === false) {
      baseStyle.opacity = 0.5;
    }

    return baseStyle;
  };

  const getInputTextStyles = (): TextStyle => {
    let baseTextStyle: TextStyle;

    // Size-based text styles
    switch (size) {
      case 'small':
        baseTextStyle = { ...textStyles.bodySmall };
        break;
      case 'large':
        baseTextStyle = { ...textStyles.bodyLarge };
        break;
      default:
        baseTextStyle = { ...textStyles.body };
    }

    baseTextStyle.color = colors.text;
    baseTextStyle.flex = 1;

    return baseTextStyle;
  };

  const getLabelStyles = (): TextStyle => {
    return {
      ...textStyles.label,
      color: error ? colors.error : colors.textSecondary,
      marginBottom: spacing['2'],
    };
  };

  const getHelperTextStyles = (): TextStyle => {
    return {
      ...textStyles.caption,
      color: error ? colors.error : colors.textSecondary,
      marginTop: spacing['1'],
    };
  };

  return (
    <View style={[styles.wrapper, containerStyle]}>
      {label && (
        <Text style={[getLabelStyles(), labelStyle]}>{label}</Text>
      )}
      
      <View style={getContainerStyles()}>
        {leftIcon && (
          <View style={[styles.iconContainer, { marginRight: spacing['2'] }]}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          style={[getInputTextStyles(), inputStyle]}
          placeholderTextColor={colors.textTertiary}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...textInputProps}
        />
        
        {rightIcon && (
          <View style={[styles.iconContainer, { marginLeft: spacing['2'] }]}>
            {rightIcon}
          </View>
        )}
      </View>
      
      {(error || helperText) && (
        <Text style={getHelperTextStyles()}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: spacing['4'],
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Input;
